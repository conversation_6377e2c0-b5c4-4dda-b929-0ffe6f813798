#!/usr/bin/env python3
"""
Options Risk Management Agent - Comprehensive Options Risk Control
"""

import asyncio
import logging
from utils.heartbeat import create_heartbeat
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Any
import json
from collections import deque

from agents.risk_management.config_manager import ConfigManager
from agents.risk_management.state_manager import StateManager, AgentState
from agents.options_market_monitoring_agent import OptionsMarketMonitoringAgent
# Lazy loaded imports:
# from agents.risk_management.risk_evaluator import RiskEvaluator
# from agents.risk_management.monitoring import Monitoring
# from agents.risk_management.risk_persistence import RiskPersistenceManager
# from agents.risk_management.trade_manager import TradeManager
# from agents.risk_management.dynamic_adjustments import DynamicAdjustments
from agents.risk_management.constants import (
    DEFAULT_INITIAL_CAPITAL,
    DEFAULT_LOT_SIZE,
    DEFAULT_RISK_SCORE,
)

logger = logging.getLogger(__name__)

class OptionsRiskManagementAgent:
    """Options Risk Management Agent for comprehensive risk control"""
    
    def __init__(self, config_path: str = "config/options_risk_management_config.yaml"):
        self.config_manager = ConfigManager(config_path)
        self.state_manager = StateManager()
        
        self.heartbeat = create_heartbeat('risk_management')
        self.agent_id = f"RiskAgent_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        self.total_capital: float = 0.0
        self.current_capital: float = 0.0
        self.daily_high_capital: float = 0.0
        self.daily_drawdown_loss: float = 0.0
        self.daily_drawdown_pct: float = 0.0
        self.trading_paused: bool = False
        self.pause_cool_off_until: Optional[datetime] = None
        self.daily_capital_used: float = 0.0
        self.daily_trades_count: int = 0
        self.monitoring_tasks: List[asyncio.Task] = []

        # Lazy loaded components
        self._risk_evaluator: Optional[RiskEvaluator] = None
        self._monitoring: Optional[Monitoring] = None
        self._risk_persistence_manager: Optional[RiskPersistenceManager] = None
        self._trade_manager: Optional[TradeManager] = None
        self._dynamic_adjustments: Optional[DynamicAdjustments] = None
        
        # Initialize Market Monitoring Agent
        self.market_monitoring_agent = OptionsMarketMonitoringAgent()

        logger.info("🛡️ [INIT] Options Risk Management Agent initialized")
    
    @property
    def risk_evaluator(self) -> RiskEvaluator:
        """Lazily loads and returns the RiskEvaluator instance."""
        if self._risk_evaluator is None:
            from agents.risk_management.risk_evaluator import RiskEvaluator
            self._risk_evaluator = RiskEvaluator(self.config_manager.config, self)
        return self._risk_evaluator

    @property
    def monitoring(self) -> Monitoring:
        """Lazily loads and returns the Monitoring instance."""
        if self._monitoring is None:
            from agents.risk_management.monitoring import Monitoring
            self._monitoring = Monitoring(self.config_manager.config, self, self._send_alert)
        return self._monitoring

    @property
    def risk_persistence_manager(self) -> RiskPersistenceManager:
        """Lazily loads and returns the RiskPersistenceManager instance."""
        if self._risk_persistence_manager is None:
            from agents.risk_management.risk_persistence import RiskPersistenceManager
            self._risk_persistence_manager = RiskPersistenceManager()
        return self._risk_persistence_manager

    @property
    def trade_manager(self) -> TradeManager:
        """Lazily loads and returns the TradeManager instance."""
        if self._trade_manager is None:
            from agents.risk_management.trade_manager import TradeManager
            # Note: emergency_safeguard_trigger needs to be bound or passed correctly.
            # Assuming it's a method of this class, it should be accessible.
            self._trade_manager = TradeManager(self.config_manager, self.state_manager, self.emergency_safeguard_trigger)
        return self._trade_manager

    @property
    def dynamic_adjustments(self) -> DynamicAdjustments:
        """Lazily loads and returns the DynamicAdjustments instance."""
        if self._dynamic_adjustments is None:
            from agents.risk_management.dynamic_adjustments import DynamicAdjustments
            self._dynamic_adjustments = DynamicAdjustments()
        return self._dynamic_adjustments

    async def initialize(self, **kwargs):
        """Initialize the agent with enhanced exception handling and logging."""
        self.state_manager.set_state(AgentState.INITIALIZING)
        try:
            logger.info("⚙️ [INIT] Loading configuration...")
            self.config_manager.load_config()
            
            logger.info("⚙️ [INIT] Setting initial capital and state...")
            self.total_capital = self.config_manager.get('initial_capital', DEFAULT_INITIAL_CAPITAL)
            self.current_capital = self.total_capital
            self.daily_high_capital = self.total_capital
            
            logger.info("⚙️ [INIT] Starting heartbeat...")
            self.heartbeat.start_heartbeat()
            
            # Initialize TradeManager with current capital and agent ID
            self.trade_manager.set_agent_capital_and_id(self.current_capital, self.agent_id)
            
            logger.info("⚙️ [INIT] Initializing Market Monitoring Agent...")
            await self.market_monitoring_agent.initialize()

            self.state_manager.set_state(AgentState.RUNNING)
            logger.info(f"✅ [SUCCESS] {self.agent_id} initialized successfully. Current capital: {self.current_capital:,.2f}")
            return True
        except FileNotFoundError as e:
            logger.error(f"❌ [INITIALIZE ERROR] Configuration file not found: {e}. Please ensure the config file exists at the specified path.", exc_info=True)
            self.state_manager.set_state(AgentState.STOPPED)
            return False
        except KeyError as e:
            logger.error(f"❌ [INITIALIZE ERROR] Missing expected key in configuration file: {e}. Please check your config file for completeness.", exc_info=True)
            self.state_manager.set_state(AgentState.STOPPED)
            return False
        except OSError as e:
            logger.error(f"❌ [INITIALIZE ERROR] OS error occurred during initialization (e.g., permissions): {e}. Ensure the agent has necessary file system permissions.", exc_info=True)
            self.state_manager.set_state(AgentState.STOPPED)
            return False
        except Exception as e:
            logger.error(f"❌ [INITIALIZE ERROR] An unexpected error occurred during agent initialization: {e}", exc_info=True)
            self.state_manager.set_state(AgentState.STOPPED)
            return False
    
    async def start(self, **kwargs) -> bool:
        """Start the risk management agent with enhanced exception handling and logging."""
        if not self.state_manager.is_running():
            logger.warning("Agent is not running. Please call initialize() first before starting.")
            return False
            
        try:
            logger.info("🚀 [START] Starting Options Risk Management Agent...")
            await self._reset_daily_metrics()

            self.trading_mode = kwargs.get('trading_mode', 'real')
            self.virtual_account = kwargs.get('virtual_account')

            logger.info(f"🛡️ [MODE] Risk management mode set to: {self.trading_mode.upper()}")
            if self.virtual_account:
                logger.info(f"🗄️ [VIRTUAL] Using virtual account: {self.virtual_account}")

            logger.info("🚀 [START] Launching monitoring tasks...")
            self.monitoring_tasks = [
                asyncio.create_task(self.monitoring.monitor_daily_drawdown()),
                asyncio.create_task(self.monitoring.monitor_portfolio_exposure()),
                # TODO: Implement full Greek monitoring logic in Monitoring class
                asyncio.create_task(self.monitoring.monitor_greeks_exposure()),
                # TODO: Implement full alert generation logic in Monitoring class
                asyncio.create_task(self.monitoring.generate_risk_alerts()),
                asyncio.create_task(self.monitoring.broadcast_risk_summary()),
                asyncio.create_task(self._monitor_active_positions()),
                asyncio.create_task(self._make_entry_exit_hold_decisions())
            ]
            logger.info(f"🚀 [START] Successfully launched {len(self.monitoring_tasks)} monitoring tasks.")

            logger.info("✅ [SUCCESS] Risk Management Agent started successfully.")
            return True
        except asyncio.CancelledError:
            logger.info("Agent start process was cancelled.")
            self.state_manager.set_state(AgentState.STOPPED)
            return False
        except Exception as e:
            logger.error(f"❌ [START ERROR] Failed to start agent: {e}", exc_info=True)
            self.state_manager.set_state(AgentState.STOPPED)
            return False

    async def cleanup(self):
        """
        Cleanup the agent and stop all background tasks with enhanced exception handling and logging.
        """
        self.state_manager.set_state(AgentState.STOPPING)
        try:
            logger.info("🛑 [CLEANUP] Initiating shutdown sequence for Risk Management Agent...")
            
            # Stop heartbeat if it exists
            if hasattr(self, 'heartbeat') and self.heartbeat:
                logger.debug("Stopping heartbeat...")
                self.heartbeat.stop_heartbeat()
                logger.debug("Heartbeat stopped.")

            # Cancel all monitoring tasks
            logger.debug(f"Cancelling {len(self.monitoring_tasks)} monitoring tasks...")
            for task in self.monitoring_tasks:
                if not task.done():
                    task.cancel()
            
            # Wait for tasks to complete cancellation
            if self.monitoring_tasks:
                await asyncio.gather(*self.monitoring_tasks, return_exceptions=True)
                logger.debug("All monitoring tasks have been cancelled and awaited.")

            # Update agent state to stopped
            self.state_manager.set_state(AgentState.STOPPED)
            logger.info("✅ [CLEANUP] Risk Management Agent has been stopped successfully.")
            
        except asyncio.CancelledError:
            # This is expected if cleanup itself is cancelled
            logger.info("Cleanup process was cancelled.")
            self.state_manager.set_state(AgentState.STOPPED) # Ensure state is set to stopped
        except Exception as e:
            # Catch any other unexpected errors during cleanup
            logger.error(f"❌ [CLEANUP ERROR] An unexpected error occurred during agent cleanup: {e}", exc_info=True)
            self.state_manager.set_state(AgentState.STOPPED) # Ensure state is set to stopped even on error
    
    async def _send_alert(self, title: str, message: str):
        """
        Sends an alert to an external system (e.g., Telegram, UI).
        Includes enhanced error handling and logging.
        """
        try:
            # TODO: Integrate with actual alerting systems (e.g., Telegram, Email, Dashboard)
            # For now, we log it and can extend this later.
            full_alert_message = f"🔔 ALERT: {title} - {message}"
            logger.warning(full_alert_message, extra={"alert_title": title, "alert_message": message})
            # Example of potential external integration (commented out)
            # await self.alerting_system.send_notification(title, message)
            logger.debug(f"Alert '{title}' sent successfully.")
        except Exception as e:
            logger.error(f"❌ [ERROR] Failed to send alert '{title}': {e}", exc_info=True)
            # Depending on criticality, might want to queue the alert for retry or escalate

    async def evaluate_signal_for_risk(self, signal: Dict[str, Any]) -> Dict[str, Any]:
        """
        Evaluates a trading signal for risk, logs the decision, and saves overrides if necessary.
        Includes enhanced exception handling for robustness.
        """
        try:
            decision = await self.risk_evaluator.evaluate_signal_for_risk(signal)
            await self.trade_manager.log_trade_decision(signal, decision)
            
            # Save override if trade is blocked or lot size is adjusted
            if not decision.get('approved', False) or decision.get('adjusted_lot_size', signal.get('lot_size', 1)) != signal.get('lot_size', 1):
                await self._save_risk_override(signal, decision)
            
            return decision
        except Exception as e:
            logger.error(f"❌ [EVALUATE RISK ERROR] Failed to evaluate signal for risk (Signal ID: {signal.get('signal_id', 'N/A')}): {e}", exc_info=True)
            # Return a default decision indicating failure or block
            return {
                'approved': False,
                'downscaled': True,
                'reason': f"Error during risk evaluation: {e}",
                'risk_score': DEFAULT_RISK_SCORE,
                'adjusted_lot_size': 0,
                'llm_explanation': 'Error during risk evaluation.'
            }
    
    async def _save_risk_override(self, signal: Dict[str, Any], risk_decision: Dict[str, Any]):
        """Save risk management override for signal generation agent using RiskPersistenceManager."""
        try:
            await self.risk_persistence_manager.save_risk_override(signal, risk_decision)
        except Exception as e:
            logger.error(f"❌ [SAVE OVERRIDE ERROR] Failed to save risk override for signal {signal.get('signal_id', 'N/A')}: {e}", exc_info=True)

    async def adjust_stop_loss_target(self, trade: Dict[str, Any]) -> Dict[str, Any]:
        """
        Feature 6: Dynamic Stop Loss / Target Adjustment using DynamicAdjustments module.
        """
        # This method would typically interact with the DynamicAdjustments module
        # For now, we'll assume it's a pass-through or a placeholder for future dynamic adjustments.
        # The actual logic for dynamic adjustments would be in dynamic_adjustments.py
        if self._dynamic_adjustments:
            return await self.dynamic_adjustments.adjust_stop_loss_target(trade)
        else:
            logger.warning("DynamicAdjustments module not loaded. Cannot adjust stop loss/target dynamically.")
            return trade # Return original trade if module not available

    async def emergency_safeguard_trigger(self, reason: str):
        """
        Triggers a trading halt and sends shutdown commands.
        """
        logger.critical(f"🚨🚨🚨 [EMERGENCY] Activating emergency safeguard: {reason}! Halting all trading operations.")
        self.state_manager.set_state(AgentState.STOPPING)
        self.trading_paused = True

        logger.critical("🛑 [SHUTDOWN] Sending shutdown command to other agents.")
        
        await self._send_alert("EMERGENCY HALT", f"System-wide trading halt triggered: {reason}")
        
        self.trade_manager.get_trade_history().append({ # Use trade_manager's history
            "timestamp": datetime.now().isoformat(),
            "event_type": "EMERGENCY_HALT",
            "reason": reason
        })

    async def update_trade_status(self, trade_update: Dict[str, Any]):
        """
        Updates the status of an active trade using the TradeManager.
        """
        await self.trade_manager.update_trade_status(trade_update)
            self.current_capital = self.trade_manager.current_capital # Update agent's capital

    async def _get_current_price_from_market_monitoring(self, symbol: str) -> Optional[float]:
        """
        Fetches the current price for a given symbol from the market monitoring agent.
        """
        try:
            # Assuming market_monitoring_agent has a method to get the latest price
            # This might involve looking up the latest OHLC bar's close price for a specific timeframe
            # For simplicity, let's try to get the latest 1-minute close price.
            latest_data = self.market_monitoring_agent.get_latest_market_data(symbol, '1min')
            if latest_data and not latest_data.is_empty():
                return latest_data['close'].to_list()[-1]
            return None
        except Exception as e:
            logger.error(f"❌ [MARKET DATA ERROR] Failed to get current price for {symbol} from market monitoring: {e}", exc_info=True)
            return None

    async def _monitor_active_positions(self):
        """
        Monitors active positions for real-time adjustments or alerts.
        Includes enhanced exception handling and logging.
        """
        while self.state_manager.is_running():
            try:
                active_trades = self.trade_manager.get_active_trades()
                if active_trades:
                    logger.info(f"📊 [POSITIONS] Monitoring {len(active_trades)} active trades.")
                    for trade_id, trade_details in list(self.trade_manager.active_trades.items()):
                        # In a real system, 'current_price' would come from a market data feed
                        # For simulation, let's assume we can get a simulated current price
                        # or that the trade_details are updated externally.
            # Get current price from market monitoring agent
            current_price = await self._get_current_price_from_market_monitoring(trade_details.get('symbol'))
            
            if current_price is None:
                logger.warning(f"Could not get live price for {trade_details.get('symbol')}. Skipping exit condition check for trade {trade_id}.")
                continue

            exit_reason = await self.trade_manager.check_exit_conditions(trade_id, current_price)
            if exit_reason:
                pnl = (current_price - trade_details['entry_price']) * trade_details['lot_size']
                await self.trade_manager.update_trade_status({
                    "trade_id": trade_id,
                    "status": "closed",
                    "pnl": pnl,
                    "exit_reason": exit_reason,
                    "exit_price": current_price
                })
                await self._send_alert(f"Trade Exit: {exit_reason}", f"Trade {trade_id} for {trade_details.get('symbol')} closed at {current_price:.2f} with PnL: {pnl:.2f}")
                else:
                    logger.debug("📊 [POSITIONS] No active trades to monitor.")
                
                # Sleep for the configured monitoring interval
                await asyncio.sleep(self.config_manager.get('monitoring_interval', 60)) # Default to 60 seconds if not found
            
            except asyncio.CancelledError:
                logger.info("Active positions monitoring task cancelled.")
                break # Exit loop if task is cancelled
            except Exception as e:
                logger.error(f"❌ [MONITOR POSITIONS ERROR] An error occurred while monitoring active positions: {e}", exc_info=True)
                # Continue monitoring even if an error occurs, to avoid bringing down the agent
                await asyncio.sleep(self.config_manager.get('monitoring_interval', 60)) # Wait before retrying

    async def _make_entry_exit_hold_decisions(self):
        """
        Entry/Exit/Hold Decision Logic for Options Buying Strategy.
        Includes enhanced exception handling and logging.
        """
        while self.state_manager.is_running():
            try:
                logger.debug("🧠 [DECISION] Evaluating entry/exit/hold decisions...")
                
                # --- Simulate receiving a signal ---
                # In a real system, this would come from a signal generation agent or market data.
                # For demonstration, we'll create a dummy signal.
                dummy_signal = {
                    "signal_id": f"SIG_{datetime.now().strftime('%H%M%S')}",
                    "symbol": "SPY",
                    "option_type": "call",
                    "strike_price": 450.0,
                    "expiry_date": (datetime.now() + timedelta(days=30)).isoformat(),
                    "entry_price": 2.50, # Premium
                    "lot_size": 10,
                    "greeks": {"delta": 0.5, "gamma": 0.1, "theta": -0.05, "vega": 0.2},
                    "implied_volatility": 0.20,
                    "time_to_expiry_days": 30,
                    "stop_loss_price": 1.50, # Example SL for the option premium
                    "take_profit_price": 4.00, # Example TP for the option premium
                    "trailing_stop_pct": 0.20 # 20% trailing stop
                }
                logger.info(f"Simulated signal received: {dummy_signal['signal_id']} for {dummy_signal['symbol']}")

                # --- Evaluate signal for risk ---
                risk_decision = await self.evaluate_signal_for_risk(dummy_signal)
                
                if risk_decision['approved'] and risk_decision['adjusted_lot_size'] > 0:
                    logger.info(f"✅ [DECISION] Signal {dummy_signal['signal_id']} approved. Adjusted lot size: {risk_decision['adjusted_lot_size']}")
                    
                    # Create a new trade based on the approved signal
                    new_trade = {
                        "trade_id": f"TRADE_{dummy_signal['signal_id']}",
                        "signal_id": dummy_signal['signal_id'],
                        "symbol": dummy_signal['symbol'],
                        "option_type": dummy_signal['option_type'],
                        "entry_price": dummy_signal['entry_price'],
                        "lot_size": risk_decision['adjusted_lot_size'],
                        "status": "open",
                        "timestamp": datetime.now().isoformat(),
                        "greeks": dummy_signal['greeks'],
                        "current_price": dummy_signal['entry_price'] # Initialize current price
                    }
                    self.trade_manager.add_active_trade(new_trade)
                    
                    # Set stop loss, take profit, and trailing stop for the new trade
                    self.trade_manager.set_trade_exit_parameters(
                        trade_id=new_trade['trade_id'],
                        stop_loss_price=dummy_signal.get('stop_loss_price'),
                        take_profit_price=dummy_signal.get('take_profit_price'),
                        trailing_stop_pct=dummy_signal.get('trailing_stop_pct')
                    )
                    logger.info(f"🚀 [TRADE] New trade {new_trade['trade_id']} opened with {new_trade['lot_size']} contracts.")
                else:
                    logger.warning(f"❌ [DECISION] Signal {dummy_signal['signal_id']} rejected: {risk_decision['reason']}")

                await asyncio.sleep(self.config_manager.get('decision_making_interval', 300)) # Sleep for a longer interval for decision making
            
            except asyncio.CancelledError:
                logger.info("Entry/Exit/Hold decision task cancelled.")
                break # Exit loop if task is cancelled
            except Exception as e:
                logger.error(f"❌ [DECISION MAKING ERROR] An error occurred during entry/exit/hold decision making: {e}", exc_info=True)
                # Continue making decisions even if an error occurs
                await asyncio.sleep(self.config_manager.get('decision_making_interval', 300)) # Wait before retrying

    async def _reset_daily_metrics(self):
        """
        Resets daily metrics at the start of a new trading day.
        Includes enhanced exception handling and logging.
        """
        try:
            now = datetime.now()
            # Check if it's a new day or if the reset date hasn't been set yet
            if not hasattr(self, '_last_daily_reset_date') or self._last_daily_reset_date != now.date():
                logger.info("☀️ [DAILY RESET] Resetting daily risk metrics and trade manager stats.")
                
                # Reset agent-specific daily metrics
                self.daily_high_capital = self.current_capital
                self.daily_drawdown_loss = 0.0
                self.daily_drawdown_pct = 0.0
                self.daily_capital_used = 0.0
                self.daily_trades_count = 0
                
                # Reset TradeManager specific metrics
                self.trade_manager.win_loss_streak.clear()
                self.trade_manager.consecutive_sl_hits = 0
                self.trade_manager.last_trade_time.clear()

                # Reset trading pause status
                self.trading_paused = False
                self.pause_cool_off_until = None
                
                # Update the last reset date
                self._last_daily_reset_date = now.date()
                logger.info("✅ [DAILY RESET] Daily metrics and trade manager stats reset successfully.")
            else:
                logger.debug("☀️ [DAILY RESET] No reset needed, today's metrics already processed.")
        except Exception as e:
            logger.error(f"❌ [DAILY RESET ERROR] Failed to reset daily metrics: {e}", exc_info=True)
            # Depending on the severity, you might want to set a flag or take other actions

# Example usage
async def main():
    agent = OptionsRiskManagementAgent()
    try:
        await agent.initialize()
        await agent.start()
        # ... simulation logic ...
    except KeyboardInterrupt:
        logger.info("Agent interrupted by user")
    finally:
        await agent.cleanup()

if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    asyncio.run(main())
