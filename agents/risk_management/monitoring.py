import asyncio
import logging
from typing import Dict, List, Optional, Any

logger = logging.getLogger(__name__)

class Monitoring:
    """
    Monitors various aspects of the trading system for risk management.
    """
    def __init__(self, config: Dict[str, Any], agent_instance: Any, send_alert_callback: callable):
        self.config = config
        self.agent_instance = agent_instance # To access agent's capital, etc.
        self._send_alert = send_alert_callback
        logger.info("Monitoring initialized.")

    async def monitor_daily_drawdown(self):
        """
        Monitors daily drawdown and triggers alerts if thresholds are breached.
        """
        logger.debug("Starting daily drawdown monitoring.")
        while self.agent_instance.state_manager.is_running():
            try:
                # Placeholder logic for drawdown monitoring
                # In a real scenario, this would involve tracking capital fluctuations
                # and comparing against configured drawdown limits.
                
                # Example: Check if current capital is significantly lower than daily high
                if self.agent_instance.current_capital < self.agent_instance.daily_high_capital * 0.95: # 5% drawdown
                    drawdown_loss = self.agent_instance.daily_high_capital - self.agent_instance.current_capital
                    drawdown_pct = (drawdown_loss / self.agent_instance.daily_high_capital) * 100
                    
                    self.agent_instance.daily_drawdown_loss = drawdown_loss
                    self.agent_instance.daily_drawdown_pct = drawdown_pct

                    if drawdown_pct > self.config.get('max_daily_drawdown_pct', 5.0): # Configurable threshold
                        await self._send_alert(
                            "Daily Drawdown Alert",
                            f"Daily drawdown limit breached: {drawdown_pct:.2f}% (Loss: {drawdown_loss:.2f})"
                        )
                        # Potentially trigger emergency safeguard or pause trading
                        # await self.agent_instance.emergency_safeguard_trigger("Daily drawdown exceeded limit")
                
                await asyncio.sleep(self.config.get('monitoring_interval', 60))
            except asyncio.CancelledError:
                logger.info("Daily drawdown monitoring task cancelled.")
                break
            except Exception as e:
                logger.error(f"❌ [MONITOR DRAWDOWN ERROR] Error in daily drawdown monitoring: {e}", exc_info=True)
                await asyncio.sleep(self.config.get('monitoring_interval', 60)) # Wait before retrying

    async def monitor_portfolio_exposure(self):
        """
        Monitors overall portfolio exposure against configured limits.
        """
        logger.debug("Starting portfolio exposure monitoring.")
        while self.agent_instance.state_manager.is_running():
            try:
                # Placeholder logic for portfolio exposure
                # This would involve calculating total exposure across all positions
                # and comparing it against limits defined in the configuration.
                
                # Example: Check if total capital used exceeds a certain percentage
                if self.agent_instance.daily_capital_used > self.agent_instance.total_capital * 0.8: # 80% of total capital used
                    await self._send_alert(
                        "Portfolio Exposure Alert",
                        f"High portfolio exposure: {self.agent_instance.daily_capital_used:.2f} used out of {self.agent_instance.total_capital:.2f}"
                    )
                
                await asyncio.sleep(self.config.get('monitoring_interval', 60))
            except asyncio.CancelledError:
                logger.info("Portfolio exposure monitoring task cancelled.")
                break
            except Exception as e:
                logger.error(f"❌ [MONITOR EXPOSURE ERROR] Error in portfolio exposure monitoring: {e}", exc_info=True)
                await asyncio.sleep(self.config.get('monitoring_interval', 60))

    async def monitor_greeks_exposure(self):
        """
        Monitors Greek exposures (Delta, Gamma, Theta, Vega) against limits.
        """
        logger.debug("Starting Greeks exposure monitoring.")
        
        # Get configuration for Greeks monitoring
        greeks_config = self.config.get('risk_management', {}).get('greeks', {})
        greeks_monitoring_enabled = greeks_config.get('enabled', False)
        
        if not greeks_monitoring_enabled:
            logger.debug("Greeks monitoring is disabled in configuration.")
            # Sleep for a short interval to avoid busy-waiting if disabled
            await asyncio.sleep(self.config.get('monitoring_interval', 60))
            return

        # Get Greek exposure limits from config
        max_delta_exposure = greeks_config.get('max_delta_exposure', 1000.0)
        max_gamma_exposure = greeks_config.get('max_gamma_exposure', 500.0)
        max_theta_exposure = greeks_config.get('max_theta_exposure', 200.0)
        max_vega_exposure = greeks_config.get('max_vega_exposure', 500.0)
        greeks_alert_threshold_pct = greeks_config.get('alert_threshold_pct', 0.8) # 80% of max limit

        while self.agent_instance.state_manager.is_running():
            try:
                active_trades = self.agent_instance.trade_manager.get_active_trades()
                
                total_delta = 0.0
                total_gamma = 0.0
                total_theta = 0.0
                total_vega = 0.0
                
                for trade in active_trades:
                    # Assuming 'greeks' are stored in the trade details, updated by a market data agent
                    greeks = trade.get('greeks', {})
                    total_delta += greeks.get('delta', 0.0) * trade.get('lot_size', 1)
                    total_gamma += greeks.get('gamma', 0.0) * trade.get('lot_size', 1)
                    total_theta += greeks.get('theta', 0.0) * trade.get('lot_size', 1)
                    total_vega += greeks.get('vega', 0.0) * trade.get('lot_size', 1)

                alert_messages = []

                # Check Delta exposure
                if abs(total_delta) > max_delta_exposure * greeks_alert_threshold_pct:
                    alert_messages.append(f"Delta exposure ({total_delta:.2f}) exceeds {greeks_alert_threshold_pct*100:.0f}% of limit ({max_delta_exposure:.2f}).")
                
                # Check Gamma exposure
                if abs(total_gamma) > max_gamma_exposure * greeks_alert_threshold_pct:
                    alert_messages.append(f"Gamma exposure ({total_gamma:.2f}) exceeds {greeks_alert_threshold_pct*100:.0f}% of limit ({max_gamma_exposure:.2f}).")

                # Check Theta exposure (often negative for long options, positive for short)
                # We might want to monitor for excessive negative theta (decay) for long positions
                # or excessive positive theta for short positions if it's not desired.
                # For simplicity, let's monitor absolute value for now.
                if abs(total_theta) > max_theta_exposure * greeks_alert_threshold_pct:
                    alert_messages.append(f"Theta exposure ({total_theta:.2f}) exceeds {greeks_alert_threshold_pct*100:.0f}% of limit ({max_theta_exposure:.2f}).")

                # Check Vega exposure
                if abs(total_vega) > max_vega_exposure * greeks_alert_threshold_pct:
                    alert_messages.append(f"Vega exposure ({total_vega:.2f}) exceeds {greeks_alert_threshold_pct*100:.0f}% of limit ({max_vega_exposure:.2f}).")

                if alert_messages:
                    await self._send_alert(
                        "Greeks Exposure Alert",
                        " ".join(alert_messages)
                    )
                    logger.warning(f"Greeks Exposure Alert triggered: {' '.join(alert_messages)}")
                else:
                    logger.debug("Greeks exposure within limits.")

                await asyncio.sleep(self.config.get('monitoring_interval', 60))
            except asyncio.CancelledError:
                logger.info("Greeks exposure monitoring task cancelled.")
                break
            except Exception as e:
                logger.error(f"❌ [MONITOR GREEKS ERROR] Error in Greeks exposure monitoring: {e}", exc_info=True)
                await asyncio.sleep(self.config.get('monitoring_interval', 60))

    async def generate_risk_alerts(self):
        """
        Generates consolidated risk alerts based on monitored conditions.
        """
        logger.debug("Starting risk alert generation.")
        
        alert_consolidation_interval = self.config.get('risk_management', {}).get('alert_consolidation_interval', 300) # 5 minutes
        critical_drawdown_threshold = self.config.get('risk_management', {}).get('critical_drawdown_pct', 15.0) # 15%

        while self.agent_instance.state_manager.is_running():
            try:
                # Check for critical conditions that might require immediate attention
                critical_alerts = []
                
                # Check for high daily drawdown
                if self.agent_instance.daily_drawdown_pct > critical_drawdown_threshold:
                    critical_alerts.append(
                        f"CRITICAL: Daily drawdown ({self.agent_instance.daily_drawdown_pct:.2f}%) exceeds critical threshold ({critical_drawdown_threshold:.1f}%)."
                    )
                
                # Check if trading is paused due to an emergency
                if self.agent_instance.trading_paused:
                    critical_alerts.append("CRITICAL: Trading is currently paused.")

                # If there are critical alerts, send a consolidated message
                if critical_alerts:
                    await self._send_alert(
                        "Consolidated Risk Alert",
                        "\n".join(critical_alerts)
                    )
                    logger.warning(f"Consolidated Risk Alert sent: {' '.join(critical_alerts)}")

                await asyncio.sleep(alert_consolidation_interval)
            except asyncio.CancelledError:
                logger.info("Risk alert generation task cancelled.")
                break
            except Exception as e:
                logger.error(f"❌ [GENERATE ALERTS ERROR] Error in risk alert generation: {e}", exc_info=True)
                await asyncio.sleep(alert_consolidation_interval)

    async def broadcast_risk_summary(self):
        """
        Broadcasts a summary of the current risk status.
        """
        logger.debug("Starting risk summary broadcast.")
        while self.agent_instance.state_manager.is_running():
            try:
                # Placeholder for broadcasting risk summary
                # This could be sent to a UI, a dashboard, or other agents.
                summary = {
                    "agent_id": self.agent_instance.agent_id,
                    "current_capital": self.agent_instance.current_capital,
                    "daily_drawdown_pct": self.agent_instance.daily_drawdown_pct,
                    "trading_paused": self.agent_instance.trading_paused,
                    "state": self.agent_instance.state_manager.get_state().name
                }
                logger.info(f"📊 [RISK SUMMARY] {summary}")
                
                await asyncio.sleep(self.config.get('summary_broadcast_interval', 120)) # e.g., every 2 minutes
            except asyncio.CancelledError:
                logger.info("Risk summary broadcast task cancelled.")
                break
            except Exception as e:
                logger.error(f"❌ [BROADCAST SUMMARY ERROR] Error broadcasting risk summary: {e}", exc_info=True)
                await asyncio.sleep(self.config.get('summary_broadcast_interval', 120))
